[2025-07-14 05:14:07] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:14:06, 2025-07-14 05:14:06, 2025-07-14 05:14:06)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:14:06, 2025-07-14 05:14:06, 2025-07-14 05:14:06)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:14:24] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:14:24, 2025-07-14 05:14:24, 2025-07-14 05:14:24)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:14:24, 2025-07-14 05:14:24, 2025-07-14 05:14:24)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:53:49] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:53:49, 2025-07-14 05:53:49, 2025-07-14 05:53:49)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:53:49, 2025-07-14 05:53:49, 2025-07-14 05:53:49)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:59:27] local.ERROR: Call to undefined method App\Http\Controllers\Peserta\PendaftaranController::edit() {"userId":6,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Peserta\\PendaftaranController::edit() at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Peserta\\PendaftaranController), 'edit')
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'peserta')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#58 {main}
"} 
[2025-07-14 07:07:33] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi (Connection: sqlite, SQL: insert into "pembayaran" ("id_pendaftaran", "jumlah_bayar", "metode_pembayaran", "status_pembayaran", "tanggal_bayar", "updated_at", "created_at") values (2, 0.00, transfer, pending, 2025-07-14 07:07:33, 2025-07-14 07:07:33, 2025-07-14 07:07:33)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi (Connection: sqlite, SQL: insert into \"pembayaran\" (\"id_pendaftaran\", \"jumlah_bayar\", \"metode_pembayaran\", \"status_pembayaran\", \"tanggal_bayar\", \"updated_at\", \"created_at\") values (2, 0.00, transfer, pending, 2025-07-14 07:07:33, 2025-07-14 07:07:33, 2025-07-14 07:07:33)) at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pembayaran')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pembayaran')
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pembayaran))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pembayaran), Object(Closure))
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(117): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pembayaran')
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pembayaran')
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pembayaran))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pembayaran), Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(117): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-15 01:34:23] local.ERROR: Call to undefined method App\Http\Controllers\Admin\PendaftaranController::store() {"userId":1,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Admin\\PendaftaranController::store() at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\PendaftaranController), 'store')
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'superadmin', 'admin')
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#58 {main}
"} 
