<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\VerificationType;
use App\Models\ParticipantVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class ParticipantVerificationController extends Controller
{
    /**
     * Check if current user can manage participant verifications
     */
    private function checkPermission()
    {
        if (!in_array(Auth::user()->role, ['admin', 'superadmin'])) {
            abort(403, 'Akses ditolak. Hanya admin dan superadmin yang dapat mengelola verifikasi peserta.');
        }
    }

    /**
     * Display participant verification dashboard
     */
    public function index(Request $request): Response
    {
        $this->checkPermission();

        $query = Peserta::with(['verifications.verificationType', 'pendaftaran.golongan.cabangLomba']);

        // Filter by verification status
        if ($request->filled('verification_status')) {
            $status = $request->verification_status;
            if ($status === 'complete') {
                $query->whereHas('verifications', function($q) {
                    $q->where('status', 'verified');
                });
            } elseif ($status === 'incomplete') {
                $query->whereDoesntHave('verifications', function($q) {
                    $q->where('status', 'verified');
                });
            }
        }

        // Filter by verification type
        if ($request->filled('verification_type')) {
            $query->whereHas('verifications.verificationType', function($q) use ($request) {
                $q->where('code', $request->verification_type);
            });
        }

        // Search by participant name or NIK
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_peserta', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $participants = $query->paginate(20);
        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/ParticipantVerifications/Index', [
            'participants' => $participants,
            'verificationTypes' => $verificationTypes,
            'filters' => $request->only(['verification_status', 'verification_type', 'search'])
        ]);
    }

    /**
     * Show participant verification details
     */
    public function show(Peserta $peserta): Response
    {
        $this->checkPermission();

        $peserta->load([
            'verifications.verificationType',
            'verifications.verifiedBy',
            'pendaftaran.golongan.cabangLomba'
        ]);

        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/ParticipantVerifications/Show', [
            'peserta' => $peserta,
            'verificationTypes' => $verificationTypes
        ]);
    }

    /**
     * Verify participant for specific verification type
     */
    public function verify(Request $request, Peserta $peserta)
    {
        $this->checkPermission();

        $validated = $request->validate([
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000',
            'verification_data' => 'nullable|array'
        ]);

        // Create or update verification record
        ParticipantVerification::updateOrCreate(
            [
                'id_peserta' => $peserta->id_peserta,
                'verification_type_id' => $validated['verification_type_id']
            ],
            [
                'status' => $validated['status'],
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'notes' => $validated['notes'],
                'verification_data' => $validated['verification_data'] ?? []
            ]
        );

        $verificationType = VerificationType::find($validated['verification_type_id']);
        $statusText = $validated['status'] === 'verified' ? 'diverifikasi' : 'ditolak';

        return back()->with('success', "Verifikasi {$verificationType->name} berhasil {$statusText}.");
    }

    /**
     * Bulk verify participants with enhanced error handling and progress tracking
     */
    public function bulkVerify(Request $request)
    {
        $this->checkPermission();

        $validated = $request->validate([
            'participant_ids' => 'required|array|max:100', // Limit to 100 participants per batch
            'participant_ids.*' => 'exists:peserta,id_peserta',
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000'
        ]);

        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $processedIds = [];

        try {
            foreach ($validated['participant_ids'] as $participantId) {
                try {
                    // Check if participant exists and is eligible for verification
                    $participant = Peserta::find($participantId);
                    if (!$participant) {
                        $errors[] = "Peserta dengan ID {$participantId} tidak ditemukan";
                        $errorCount++;
                        continue;
                    }

                    // Create or update verification record
                    ParticipantVerification::updateOrCreate(
                        [
                            'id_peserta' => $participantId,
                            'verification_type_id' => $validated['verification_type_id']
                        ],
                        [
                            'status' => $validated['status'],
                            'verified_by' => Auth::id(),
                            'verified_at' => now(),
                            'notes' => $validated['notes']
                        ]
                    );

                    $processedIds[] = $participantId;
                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = "Gagal memverifikasi peserta ID {$participantId}: " . $e->getMessage();
                    $errorCount++;
                }
            }

            $verificationType = VerificationType::find($validated['verification_type_id']);
            $statusText = $validated['status'] === 'verified' ? 'diverifikasi' : 'ditolak';

            // Prepare response message
            $message = "Berhasil {$statusText} {$successCount} peserta untuk {$verificationType->name}.";
            if ($errorCount > 0) {
                $message .= " {$errorCount} peserta gagal diproses.";
            }

            $response = [
                'success' => $successCount > 0,
                'message' => $message,
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'total_count' => count($validated['participant_ids']),
                    'processed_ids' => $processedIds,
                    'errors' => $errors,
                    'verification_type' => $verificationType->name,
                    'status' => $validated['status']
                ]
            ];

            if ($errorCount > 0 && $successCount === 0) {
                return back()->with('error', $message)->with('bulk_errors', $errors);
            }

            return back()->with('success', $message)->with('bulk_result', $response['data']);

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat memverifikasi peserta: ' . $e->getMessage());
        }
    }

    /**
     * Get verification statistics for real-time updates
     */
    public function getVerificationStats(Request $request)
    {
        $this->checkPermission();

        $verificationType = null;
        if ($request->filled('verification_type_id')) {
            $verificationType = VerificationType::find($request->verification_type_id);
        }

        // Base query for participants
        $participantsQuery = Peserta::with(['verifications.verificationType', 'pendaftaran.golongan.cabangLomba']);

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $participantsQuery->where(function($q) use ($search) {
                $q->where('nama_peserta', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $totalParticipants = $participantsQuery->count();

        // Get verification statistics
        $stats = [
            'total_participants' => $totalParticipants,
            'pending_verification' => 0,
            'verified_count' => 0,
            'rejected_count' => 0
        ];

        if ($verificationType) {
            // Count participants by verification status for specific type
            $verifiedCount = ParticipantVerification::where('verification_type_id', $verificationType->id_verification_type)
                ->where('status', 'verified')
                ->count();

            $rejectedCount = ParticipantVerification::where('verification_type_id', $verificationType->id_verification_type)
                ->where('status', 'rejected')
                ->count();

            $stats['verified_count'] = $verifiedCount;
            $stats['rejected_count'] = $rejectedCount;
            $stats['pending_verification'] = $totalParticipants - $verifiedCount - $rejectedCount;
        }

        return response()->json([
            'success' => true,
            'data' => $stats,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * NIK verification interface
     */
    public function nikVerification(Request $request): Response
    {
        $this->checkPermission();

        $nikVerificationType = VerificationType::where('code', 'nik')->first();

        if (!$nikVerificationType) {
            abort(404, 'Jenis verifikasi NIK tidak ditemukan.');
        }

        $query = Peserta::with(['pendaftaran.golongan.cabangLomba']);

        // Get participants without NIK verification or with pending NIK verification
        $query->where(function($q) use ($nikVerificationType) {
            $q->whereDoesntHave('verifications', function($subQ) use ($nikVerificationType) {
                $subQ->where('verification_type_id', $nikVerificationType->id_verification_type);
            })->orWhereHas('verifications', function($subQ) use ($nikVerificationType) {
                $subQ->where('verification_type_id', $nikVerificationType->id_verification_type)
                     ->where('status', 'pending');
            });
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_peserta', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $participants = $query->paginate(20);

        return Inertia::render('Admin/ParticipantVerifications/NikVerification', [
            'participants' => $participants,
            'nikVerificationType' => $nikVerificationType,
            'filters' => $request->only(['search'])
        ]);
    }
}
