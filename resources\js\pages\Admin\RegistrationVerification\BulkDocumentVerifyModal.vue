<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle>Verifikasi Dokumen Massal</DialogTitle>
        <DialogDescription>
          Anda akan memverifikasi {{ documents.length }} dokumen sekaligus.
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Document List -->
        <div class="max-h-60 overflow-y-auto border rounded p-3 space-y-2">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Dokumen yang akan diverifikasi:</h4>
          <div v-for="document in documents" :key="document.id_dokumen" class="flex items-center space-x-2 text-sm">
            <Icon name="file" class="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span class="truncate">{{ document.nama_file }}</span>
          </div>
        </div>

        <!-- Action Selection -->
        <div class="space-y-2">
          <Label for="action">Aksi Verifikasi</Label>
          <Select v-model="form.action" required>
            <SelectTrigger>
              <SelectValue placeholder="Pilih aksi verifikasi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="verified">
                <div class="flex items-center space-x-2">
                  <Icon name="check" class="w-4 h-4 text-green-600" />
                  <span>Verifikasi Semua Dokumen</span>
                </div>
              </SelectItem>
              <SelectItem value="rejected">
                <div class="flex items-center space-x-2">
                  <Icon name="x" class="w-4 h-4 text-red-600" />
                  <span>Tolak Semua Dokumen</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Notes -->
        <div class="space-y-2">
          <Label for="notes">Catatan Verifikasi</Label>
          <Textarea
            id="notes"
            v-model="form.notes"
            placeholder="Tambahkan catatan untuk verifikasi ini (opsional)"
            rows="3"
            :required="form.action === 'reject'"
          />
          <p v-if="form.action === 'reject'" class="text-xs text-red-600">
            Catatan wajib diisi untuk penolakan
          </p>
        </div>

        <!-- Warning Message -->
        <div v-if="form.action" class="p-4 rounded-lg border">
          <div v-if="form.action === 'approve'" class="flex items-start space-x-2 text-green-700 bg-green-50 p-3 rounded">
            <Icon name="checkCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Menyetujui {{ documents.length }} dokumen</p>
              <p class="text-sm text-green-600 mt-1">
                Semua dokumen akan berstatus "Disetujui" dan dapat melanjutkan proses verifikasi.
              </p>
            </div>
          </div>
          <div v-else-if="form.action === 'reject'" class="flex items-start space-x-2 text-red-700 bg-red-50 p-3 rounded">
            <Icon name="xCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Menolak {{ documents.length }} dokumen</p>
              <p class="text-sm text-red-600 mt-1">
                Semua dokumen akan berstatus "Ditolak" dan peserta perlu mengunggah ulang dokumen yang sesuai.
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            @click="$emit('update:show', false)"
          >
            Batal
          </Button>
          <Button
            type="submit"
            :disabled="!form.action || isSubmitting || (form.action === 'reject' && !form.notes.trim())"
            :class="{
              'bg-green-600 hover:bg-green-700': form.action === 'approve',
              'bg-red-600 hover:bg-red-700': form.action === 'reject'
            }"
          >
            <Icon
              v-if="isSubmitting"
              name="loader"
              class="w-4 h-4 mr-2 animate-spin"
            />
            <Icon
              v-else-if="form.action === 'approve'"
              name="check"
              class="w-4 h-4 mr-2"
            />
            <Icon
              v-else-if="form.action === 'reject'"
              name="x"
              class="w-4 h-4 mr-2"
            />
            {{ getSubmitButtonText() }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  documents: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { documentIds: number[]; action: string; notes: string }]
}>()

// Reactive data
const isSubmitting = ref(false)
const form = ref({
  action: '',
  notes: ''
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    form.value = {
      action: '',
      notes: ''
    }
  }
})

// Methods
const handleSubmit = async () => {
  if (!form.value.action) return
  if (form.value.action === 'reject' && !form.value.notes.trim()) return

  isSubmitting.value = true

  try {
    emit('verify', {
      documentIds: props.documents.map(doc => doc.id_dokumen),
      action: form.value.action,
      notes: form.value.notes
    })
  } finally {
    isSubmitting.value = false
  }
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }

  if (form.value.action === 'approve') {
    return `Setujui ${props.documents.length} Dokumen`
  } else if (form.value.action === 'reject') {
    return `Tolak ${props.documents.length} Dokumen`
  }

  return 'Verifikasi'
}
</script>
